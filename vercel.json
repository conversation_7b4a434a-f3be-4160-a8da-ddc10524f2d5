{"functions": {"api/news.js": {"runtime": "nodejs18.x"}}, "env": {"NEWS_API_KEY": "@news-api-key", "NEWSDATA_API_KEY": "@newsdata-api-key"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}
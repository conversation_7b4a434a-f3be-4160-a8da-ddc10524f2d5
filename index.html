
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>حذيفه الحذيفي - مهندس تقنية معلومات</title>
    <meta name="description" content="الموقع الشخصي لحذيفه عبدالمعز الحذيفي، مهندس تقنية معلومات متخصص في تطوير الويب وقواعد البيانات" />
    <meta name="keywords" content="مهندس تقنية معلومات, تطوير ويب, Laravel, قواعد البيانات, برمجة" />
    <meta name="author" content="حذيفه عبدالمعز الحذيفي" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/profile-photo.jpg" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="حذيفه الحذيفي - مهندس تقنية معلومات" />
    <meta property="og:description" content="الموقع الشخصي لحذيفه عبدالمعز الحذيفي، مهندس تقنية معلومات متخصص في تطوير الويب وقواعد البيانات" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://ha1234098765.github.io/hodifa-portfolio/" />
    <meta property="og:image" content="https://ha1234098765.github.io/hodifa-portfolio/profile-photo.jpg" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="حذيفه الحذيفي - مهندس تقنية معلومات" />
    <meta name="twitter:description" content="الموقع الشخصي لحذيفه عبدالمعز الحذيفي، مهندس تقنية معلومات متخصص في تطوير الويب وقواعد البيانات" />
    <meta name="twitter:image" content="https://ha1234098765.github.io/hodifa-portfolio/profile-photo.jpg" />

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي",
      "jobTitle": "مهندس تقنية معلومات",
      "email": "<EMAIL>",
      "telephone": ["+967 777548421", "+967 718706242"],
      "url": "https://ha1234098765.github.io/hodifa-portfolio/",
      "sameAs": [
        "https://www.facebook.com/share/1E3T83a8KD/",
        "https://www.linkedin.com/in/hodifa-al-hodify-30644b289",
        "https://x.com/moaz_abdh",
        "https://github.com/HA1234098765",
        "https://www.instagram.com/invites/contact/?utm_source=ig_contact_invite&utm_medium=copy_link&utm_content=mwfgwqx"
      ]
    }
    </script>

    <link rel="canonical" href="https://ha1234098765.github.io/hodifa-portfolio/" />

    <!-- Start Single Page Apps for GitHub Pages -->
    <script type="text/javascript">
      // Single Page Apps for GitHub Pages
      // MIT License
      // https://github.com/rafgraph/spa-github-pages
      // This script checks to see if a redirect is present in the query string,
      // converts it back into the correct url and adds it to the
      // browser's history using window.history.replaceState(...),
      // which won't cause the browser to attempt to load the new url.
      // When the single page app is loaded further down in this file,
      // the correct url will be waiting in the browser's history for
      // the single page app to route accordingly.
      (function(l) {
        if (l.search[1] === '/' ) {
          var decoded = l.search.slice(1).split('&').map(function(s) {
            return s.replace(/~and~/g, '&')
          }).join('?');
          window.history.replaceState(null, null,
              l.pathname.slice(0, -1) + decoded + l.hash
          );
        }
      }(window.location))
    </script>
    <!-- End Single Page Apps for GitHub Pages -->
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>

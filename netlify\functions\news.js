// Netlify Function لجلب الأخبار التقنية
const fetch = require('node-fetch');

exports.handler = async (event, context) => {
  // إعداد CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Content-Type': 'application/json'
  };

  // التعامل مع preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  // السماح فقط بـ GET requests
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // استخدام NewsAPI.org مع API key من environment variables
    const API_KEY = process.env.NEWS_API_KEY || '1660ff496c4247c3a7d49457501feb73';
    
    // جرب NewsAPI.org أولاً
    let apiUrl = `https://newsapi.org/v2/everything?q=technology+programming+software+development&language=en&sortBy=publishedAt&pageSize=20&apiKey=${API_KEY}`;
    
    console.log('Fetching from NewsAPI.org...');
    let response = await fetch(apiUrl);
    
    if (!response.ok) {
      console.log('NewsAPI.org failed, trying NewsData.io...');
      // إذا فشل NewsAPI، جرب NewsData.io
      const NEWSDATA_API_KEY = process.env.NEWSDATA_API_KEY || '1660ff496c4247c3a7d49457501feb73';
      apiUrl = `https://newsdata.io/api/1/news?apikey=${NEWSDATA_API_KEY}&language=en&category=technology&size=20`;
      response = await fetch(apiUrl);
    }

    if (!response.ok) {
      console.log('Both APIs failed, using fallback...');
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify(getFallbackNews())
      };
    }

    const data = await response.json();
    
    // تحويل البيانات إلى تنسيق موحد
    let articles = [];
    
    if (data.articles) {
      // NewsAPI.org format
      articles = data.articles.map(article => ({
        article_id: article.url,
        title: article.title,
        description: article.description,
        content: article.content,
        link: article.url,
        image_url: article.urlToImage,
        source_id: article.source?.name || 'Unknown',
        category: ['technology'],
        pubDate: article.publishedAt
      }));
    } else if (data.results) {
      // NewsData.io format
      articles = data.results.map(article => ({
        article_id: article.article_id,
        title: article.title,
        description: article.description,
        content: article.content,
        link: article.link,
        image_url: article.image_url,
        source_id: article.source_id,
        category: article.category || ['technology'],
        pubDate: article.pubDate
      }));
    }

    // فلترة المقالات
    const filteredArticles = articles
      .filter(article => 
        article.title && 
        article.description && 
        article.link &&
        !article.title.toLowerCase().includes('[removed]') &&
        !article.description.toLowerCase().includes('[removed]')
      )
      .slice(0, 12);

    console.log(`Successfully fetched ${filteredArticles.length} articles`);

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        articles: filteredArticles,
        total: filteredArticles.length,
        source: 'live_api'
      })
    };

  } catch (error) {
    console.error('Error fetching news:', error);
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(getFallbackNews())
    };
  }
};

function getFallbackNews() {
  const fallbackNews = [
    {
      article_id: "fallback_1",
      title: "AI Revolution in Software Development",
      description: "Artificial Intelligence is transforming how developers write, test, and deploy code.",
      link: "https://github.com/trending",
      image_url: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=500&h=300&fit=crop",
      source_id: "Tech News",
      category: ["technology"],
      pubDate: new Date().toISOString()
    }
  ];

  return {
    success: true,
    articles: fallbackNews,
    total: fallbackNews.length,
    source: 'fallback'
  };
}
